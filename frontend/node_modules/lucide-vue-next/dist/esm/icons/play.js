/**
 * @license lucide-vue-next v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Play = createLucideIcon("PlayIcon", [
  ["polygon", { points: "5 3 19 12 5 21 5 3", key: "191637" }]
]);

export { Play as default };
//# sourceMappingURL=play.js.map
