{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./node_modules/vue-router/dist/vue-router.d.ts", "./src/app.vue.ts", "./src/components/ui/alert-dialog.vue.ts", "./node_modules/clsx/clsx.d.mts", "./node_modules/class-variance-authority/dist/types.d.ts", "./node_modules/class-variance-authority/dist/index.d.ts", "./node_modules/tailwind-merge/dist/types.d.ts", "./src/lib/utils.ts", "./src/components/ui/button.vue.ts", "./node_modules/axios/index.d.ts", "./src/types/group.ts", "./src/services/api.ts", "./src/services/groupservice.ts", "./src/components/deleteconfirmdialog.vue.ts", "./src/components/ui/dialog.vue.ts", "./src/components/ui/input.vue.ts", "./src/components/ui/label.vue.ts", "./src/components/groupformdialog.vue.ts", "./src/components/ui/card.vue.ts", "./src/components/ui/select.vue.ts", "./src/components/ui/table.vue.ts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/@vueuse/shared/index.d.mts", "./node_modules/@vueuse/core/index.d.mts", "./node_modules/lucide-vue-next/dist/lucide-vue-next.d.ts", "./src/views/groupmanagement.vue.ts", "./__vls_types.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./src/env.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./src/main.ts"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0"], "root": [58, 59, 64, 65, [67, 77], 82, 83, 90, 92], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[48, 50, 51, 56, 57, 91], [52], [46, 52, 53], [54], [46], [46, 47, 48, 50], [47, 48, 49, 50], [78, 79], [78], [60, 61], [60], [56, 57, 91], [56, 57, 78], [88], [84], [85], [86, 87], [50, 55], [50], [51, 56, 57, 91], [51, 56, 57, 59, 65, 67, 69, 91], [51, 56, 57, 65, 67, 69, 71, 72, 73, 91], [51, 56, 57, 62, 64, 91], [51, 56, 57, 64, 91], [56, 57, 89, 91], [51, 60, 63], [51, 56, 57, 58, 82, 89, 91], [51, 66, 67], [51, 67, 68], [51], [51, 56, 57, 65, 67, 69, 70, 72, 73, 74, 75, 80, 81, 91]], "referencedMap": [[83, 1], [53, 2], [54, 3], [55, 4], [47, 5], [48, 6], [50, 7], [80, 8], [79, 9], [62, 10], [61, 11], [81, 12], [91, 13], [89, 14], [85, 15], [86, 16], [88, 17], [78, 12], [57, 12], [56, 18], [51, 19], [58, 20], [70, 21], [74, 22], [59, 20], [65, 23], [75, 24], [71, 20], [72, 24], [73, 24], [76, 24], [77, 24], [90, 25], [64, 26], [92, 27], [68, 28], [69, 29], [67, 30], [82, 31]], "exportedModulesMap": [[83, 1], [53, 2], [54, 3], [55, 4], [47, 5], [48, 6], [50, 7], [80, 8], [79, 9], [62, 10], [61, 11], [81, 12], [91, 13], [89, 14], [85, 15], [86, 16], [88, 17], [78, 12], [57, 12], [56, 18], [51, 19], [58, 20], [70, 21], [74, 22], [59, 20], [65, 23], [75, 24], [71, 20], [72, 24], [73, 24], [76, 24], [77, 24], [90, 25], [64, 26], [92, 27], [68, 28], [69, 29], [67, 30], [82, 31]], "semanticDiagnosticsPerFile": [83, 53, 52, 54, 55, 47, 48, 50, 46, 80, 79, 66, 62, 61, 60, 49, 81, 91, 63, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 89, 85, 84, 86, 87, 88, 78, 57, 56, 51, 58, 70, 74, 59, 65, 75, 71, 72, 73, 76, 77, 90, 64, 92, 68, 69, 67, 82], "affectedFilesPendingEmit": [58, 70, 74, 59, 65, 75, 71, 72, 73, 76, 77, 64, 92, 68, 69, 67, 82], "emitSignatures": [58, 59, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 82, 92]}, "version": "5.3.3"}