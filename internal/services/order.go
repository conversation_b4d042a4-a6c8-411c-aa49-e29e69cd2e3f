package services

import (
	"bytes"
	"context"
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"telegram-order-bot/internal/config"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/models"
	"time"
)

// OrderService 订单服务
type OrderService struct {
	config       *config.ThirdPartyAPIConfig
	qrCodeConfig *config.QRCodeAPIConfig
	client       *http.Client
}

// NewOrderService 创建新的订单服务
func NewOrderService(config config.ThirdPartyAPIConfig, qrCodeConfig config.QRCodeAPIConfig) *OrderService {
	return &OrderService{
		config:       &config,
		qrCodeConfig: &qrCodeConfig,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// generateRequestNo 生成唯一的请求号
func generateRequestNo() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// generateSign 生成签名
func generateSign(params map[string]interface{}, apiKey string) string {
	// 收集所有参数键并排序
	var keys []string
	for key := range params {
		if key != "sign" { // 排除sign字段本身
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 按ASCII顺序连接参数
	var parts []string
	for _, key := range keys {
		value := fmt.Sprintf("%v", params[key])
		if value != "" { // 只包含非空值
			parts = append(parts, fmt.Sprintf("%s=%s", key, value))
		}
	}

	// 连接所有参数并加上密钥
	signString := strings.Join(parts, "&") + "&key=" + apiKey

	// 计算MD5
	hash := md5.Sum([]byte(signString))
	return hex.EncodeToString(hash[:])
}

// QueryOrder 查询订单信息
func (o *OrderService) QueryOrder(merchantOrderID string) (*models.ThirdPartyOrderResponse, error) {
	logger.WithField("merchant_order_id", merchantOrderID).Info("Starting order query")

	// 构建查询请求
	queryData := map[string]interface{}{
		"mer_order_no": merchantOrderID,
		"request_no":   generateRequestNo(),           // 唯一请求号
		"request_time": time.Now().Format("20060102"), //时间格式（yyyyMMdd）
	}

	// 生成签名
	queryData["sign"] = generateSign(queryData, o.config.Key)

	// 打印请求数据日志
	logger.WithFields(map[string]interface{}{
		"merchant_order_id": merchantOrderID,
		"request_data":      queryData,
		"api_url":           o.config.BaseURL + o.config.QueryPath,
	}).Info("Order query request data")

	jsonData, err := json.Marshal(queryData)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"merchant_order_id": merchantOrderID,
			"error":             err,
		}).Error("Failed to marshal order query data")
		return nil, fmt.Errorf("failed to marshal query data: %v", err)
	}

	// 发送请求
	url := o.config.BaseURL + o.config.QueryPath
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求超时 (2秒)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	req = req.WithContext(ctx)

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	//req.Header.Set("Authorization", "Bearer "+o.config.APIKey)

	// 执行请求
	resp, err := o.client.Do(req)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"merchant_order_id": merchantOrderID,
			"error":             err,
		}).Error("Failed to execute order query request")
		return nil, fmt.Errorf("failed to execute request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"merchant_order_id": merchantOrderID,
			"error":             err,
		}).Error("Failed to read order query response")
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// 打印原始响应日志
	logger.WithFields(map[string]interface{}{
		"merchant_order_id": merchantOrderID,
		"response_body":     string(body),
		"status_code":       resp.StatusCode,
	}).Info("Order query raw response")

	// 解析响应
	var orderResponse models.ThirdPartyOrderResponse
	if err := json.Unmarshal(body, &orderResponse); err != nil {
		logger.WithFields(map[string]interface{}{
			"merchant_order_id": merchantOrderID,
			"response_body":     string(body),
			"error":             err,
		}).Error("Failed to unmarshal order query response")
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 打印解析后的订单详细信息
	logger.WithFields(map[string]interface{}{
		"merchant_order_id": merchantOrderID,
		"response_success":  orderResponse.Success,
		"response_message":  orderResponse.Message,
	}).Info("Order query response parsed")

	// 如果有订单数据，打印详细的订单信息
	if orderResponse.Data != nil {
		orderData := orderResponse.Data
		logger.WithFields(map[string]interface{}{
			"merchant_order_id":     merchantOrderID,
			"submit_order_id":       orderData.SubmitOrderID,
			"merchant_order_id_api": orderData.MerchantOrderID,
			"platform_order_id":     orderData.OrderID,
			"status":                orderData.Status,
			"amount":                orderData.Amount,
			"actual_amount":         orderData.ActualAmount,
			"payment_account":       orderData.PaymentAccount,
			"payment_institution":   orderData.PaymentInstitution,
			"payment_code":          orderData.PaymentCode,
		}).Info("Order detailed information")
	} else {
		logger.WithField("merchant_order_id", merchantOrderID).Warn("Order query response contains no data")
	}

	return &orderResponse, nil
}

// VerifyVoucher 验证凭证（二维码信息）
func (o *OrderService) VerifyVoucher(qrContent string) (*models.QRCodeScanResponse, error) {
	logger.WithField("qr_content_length", len(qrContent)).Info("Starting QR code verification")

	// 构建验证请求
	voucherData := map[string]interface{}{
		"qrString": qrContent,
	}

	// 打印请求数据日志（不包含完整二维码内容以避免日志过长）
	logger.WithFields(map[string]interface{}{
		"qr_content_preview": func() string {
			if len(qrContent) > 50 {
				return qrContent[:50] + "..."
			}
			return qrContent
		}(),
		"request_data": voucherData,
	}).Info("QR code verification request data")

	jsonData, err := json.Marshal(voucherData)
	if err != nil {
		logger.WithField("error", err).Error("Failed to marshal QR code verification data")
		return nil, fmt.Errorf("failed to marshal voucher data: %v", err)
	}

	// 发送请求
	url := o.qrCodeConfig.BaseURL + o.qrCodeConfig.AccountNo + "/qrScan"
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求超时 (2秒)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	req = req.WithContext(ctx)

	// 设置请求头
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("x-license-key", o.qrCodeConfig.LicenseKey)
	req.Header.Add("access-key", o.qrCodeConfig.AccessKey)

	// 执行请求
	resp, err := o.client.Do(req)
	if err != nil {
		// 检查是否是超时错误
		if ctx.Err() == context.DeadlineExceeded {
			logger.WithField("timeout", "30s").Error("QR code verification request timed out")
			return nil, fmt.Errorf("QR code verification request timed out after 30 seconds")
		}
		logger.WithField("error", err).Error("Failed to execute QR code verification request")
		return nil, fmt.Errorf("failed to execute request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// 记录二维码API原始响应
	logger.WithFields(map[string]interface{}{
		"qr_content":     qrContent,
		"response_body":  string(body),
		"response_code":  resp.StatusCode,
		"content_length": len(body),
	}).Info("QR Code API Response")

	// 打印body的json
	fmt.Printf("QR Code API Response Body: %s\n", string(body))

	// 解析响应
	var qrCodeResponse models.QRCodeScanResponse
	if err := json.Unmarshal(body, &qrCodeResponse); err != nil {
		logger.WithFields(map[string]interface{}{
			"qr_content":    qrContent,
			"response_body": string(body),
			"error":         err,
		}).Error("Failed to unmarshal QR code response")
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 记录解析后的结构化响应
	logger.WithFields(map[string]interface{}{
		"qr_content": qrContent,
		"status":     qrCodeResponse.Status,
		"code":       qrCodeResponse.Code,
		"message":    qrCodeResponse.Message,
		"data":       qrCodeResponse.Data,
	}).Info("QR Code Response Parsed")

	return &qrCodeResponse, nil
}
