package models

import (
	"time"
)

// GroupType 群聊类型枚举
type GroupType int

const (
	GroupTypeMerchant         GroupType = 1 // 商户群
	GroupTypeSupplier         GroupType = 2 // 供应商群
	GroupTypeCustomerService  GroupType = 3 // 客服群
)

// String 返回群聊类型的字符串表示
func (gt GroupType) String() string {
	switch gt {
	case GroupTypeMerchant:
		return "商户群"
	case GroupTypeSupplier:
		return "供应商群"
	case GroupTypeCustomerService:
		return "客服群"
	default:
		return "未知类型"
	}
}

// Group 群聊表模型
type Group struct {
	ID                uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	GroupName         string    `gorm:"type:varchar(255);not null;comment:群聊名称" json:"group_name"`
	TelegramGroupID   string    `gorm:"type:varchar(50);not null;unique;comment:Telegram群组ID" json:"telegram_group_id"`
	PaymentInstitution string   `gorm:"type:varchar(255);comment:支付机构" json:"payment_institution"`
	GroupType         GroupType `gorm:"type:tinyint;not null;comment:群聊类型(1:商户群,2:供应商群,3:客服群)" json:"group_type"`
	IsActive          bool      `gorm:"type:boolean;default:true;comment:是否启用" json:"is_active"`
	CreatedAt         time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt         time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (Group) TableName() string {
	return "groups"
}

// IsMerchantGroup 判断是否为商户群
func (g *Group) IsMerchantGroup() bool {
	return g.GroupType == GroupTypeMerchant
}

// IsSupplierGroup 判断是否为供应商群
func (g *Group) IsSupplierGroup() bool {
	return g.GroupType == GroupTypeSupplier
}

// IsCustomerServiceGroup 判断是否为客服群
func (g *Group) IsCustomerServiceGroup() bool {
	return g.GroupType == GroupTypeCustomerService
}
