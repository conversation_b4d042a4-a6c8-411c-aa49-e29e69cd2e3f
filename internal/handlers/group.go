package handlers

import (
	"net/http"
	"strconv"
	"telegram-order-bot/internal/models"
	"telegram-order-bot/internal/services"

	"github.com/gin-gonic/gin"
)

// GroupHandler 群聊管理处理器
type GroupHandler struct {
	groupService *services.GroupService
}

// NewGroupHandler 创建群聊管理处理器
func NewGroupHandler(groupService *services.GroupService) *GroupHandler {
	return &GroupHandler{
		groupService: groupService,
	}
}

// GetGroups 获取所有群聊
func (h *GroupHandler) GetGroups(c *gin.Context) {
	groupType := c.Query("type")
	paymentInstitution := c.Query("payment_institution")

	var groups []models.Group
	var err error

	switch groupType {
	case "1", "merchant":
		groups, err = h.groupService.GetMerchantGroups()
	case "2", "supplier":
		if paymentInstitution != "" {
			groups, err = h.groupService.GetSupplierGroupsByPaymentInstitution(paymentInstitution)
		} else {
			groups, err = h.groupService.GetSupplierGroups()
		}
	case "3", "customer_service":
		groups, err = h.groupService.GetCustomerServiceGroups()
	default:
		// 获取所有群聊
		// 这里需要添加一个获取所有群聊的方法
		c.JSON(http.StatusBadRequest, gin.H{"error": "Please specify group type (1=merchant, 2=supplier, 3=customer_service)"})
		return
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    groups,
		"count":   len(groups),
	})
}

// GetGroupsWithPagination 分页获取群聊列表
func (h *GroupHandler) GetGroupsWithPagination(c *gin.Context) {
	// 解析分页参数
	var req services.PaginationRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 调用服务层分页查询
	result, err := h.groupService.GetGroupsWithPagination(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result.Data,
		"pagination": gin.H{
			"total":       result.Total,
			"page":        result.Page,
			"page_size":   result.PageSize,
			"total_pages": result.TotalPages,
		},
	})
}

// GetGroup 根据ID获取群聊信息
func (h *GroupHandler) GetGroup(c *gin.Context) {
	telegramGroupID := c.Param("telegram_group_id")
	if telegramGroupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "telegram_group_id is required"})
		return
	}

	group, err := h.groupService.GetGroupByTelegramID(telegramGroupID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if group == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    group,
	})
}

// CreateGroup 创建新群聊
func (h *GroupHandler) CreateGroup(c *gin.Context) {
	var req struct {
		GroupName          string           `json:"group_name" binding:"required"`
		TelegramGroupID    string           `json:"telegram_group_id" binding:"required"`
		PaymentInstitution string           `json:"payment_institution"`
		GroupType          models.GroupType `json:"group_type" binding:"required,min=1,max=3"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	group := &models.Group{
		GroupName:          req.GroupName,
		TelegramGroupID:    req.TelegramGroupID,
		PaymentInstitution: req.PaymentInstitution,
		GroupType:          req.GroupType,
		IsActive:           true,
	}

	if err := h.groupService.CreateGroup(group); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    group,
		"message": "Group created successfully",
	})
}

// UpdateGroup 更新群聊信息
func (h *GroupHandler) UpdateGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid group ID"})
		return
	}

	var req struct {
		GroupName          string           `json:"group_name"`
		PaymentInstitution string           `json:"payment_institution"`
		GroupType          models.GroupType `json:"group_type"`
		IsActive           *bool            `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 先获取现有群聊信息
	group, err := h.groupService.GetGroupByTelegramID("")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 更新字段
	group.ID = uint(id)
	if req.GroupName != "" {
		group.GroupName = req.GroupName
	}
	if req.PaymentInstitution != "" {
		group.PaymentInstitution = req.PaymentInstitution
	}
	if req.GroupType != 0 {
		group.GroupType = req.GroupType
	}
	if req.IsActive != nil {
		group.IsActive = *req.IsActive
	}

	if err := h.groupService.UpdateGroup(group); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    group,
		"message": "Group updated successfully",
	})
}

// DeleteGroup 删除群聊（软删除）
func (h *GroupHandler) DeleteGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid group ID"})
		return
	}

	if err := h.groupService.DeleteGroup(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Group deleted successfully",
	})
}

// GetGroupTypes 获取群聊类型列表
func (h *GroupHandler) GetGroupTypes(c *gin.Context) {
	types := []map[string]interface{}{
		{"value": 1, "label": "商户群", "description": "处理订单消息的商户群"},
		{"value": 2, "label": "供应商群", "description": "处理失败订单的供应商群"},
		{"value": 3, "label": "客服群", "description": "人工客服处理群"},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    types,
	})
}
